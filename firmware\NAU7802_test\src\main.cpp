#include <Wire.h> 
 
#include "SparkFun_Qwiic_Scale_NAU7802_Arduino_Library.h" // Click here to get the library: http://librarymanager/All#SparkFun_NAU7802 
 
NAU7802 myScale; //Create instance of the NAU7802 class 
 
void setup() 
{ 
  Serial.begin(115200); 
  Serial.println("Qwiic Scale Example"); 
 
  Wire.begin(); 
 
  if (myScale.begin() == false) 
  { 
    Serial.println("Scale not detected. Please check wiring. Freezing..."); 
    while (1); 
  } 
  Serial.println("Scale detected!"); 
} 
 
void loop() 
{ 
  if(myScale.available() == true) 
  { 
    int32_t currentReading = myScale.getReading(); 
    Serial.print("Reading: "); 
    Serial.println(currentReading); 
  } 
} 