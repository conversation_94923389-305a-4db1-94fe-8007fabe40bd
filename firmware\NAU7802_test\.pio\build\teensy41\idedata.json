{"build_type": "release", "env_name": "teensy41", "libsource_dirs": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\10xconstruction\\embedded-test-framework\\firmware\\NAU7802_test\\lib", "C:\\Users\\<USER>\\OneDrive\\Desktop\\10xconstruction\\embedded-test-framework\\firmware\\NAU7802_test\\.pio\\libdeps\\teensy41", "C:\\Users\\<USER>\\.platformio\\lib", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries"], "defines": ["PLATFORMIO=60118", "__IMXRT1062__", "ARDUINO_TEENSY41", "SERIAL_PORT=Serial", "USB_SERIAL", "ARDUINO=10805", "TEENSYDUINO=159", "CORE_TEENSY", "F_CPU=600000000", "LAYOUT_US_ENGLISH"], "includes": {"build": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\10xconstruction\\embedded-test-framework\\firmware\\NAU7802_test\\include", "C:\\Users\\<USER>\\OneDrive\\Desktop\\10xconstruction\\embedded-test-framework\\firmware\\NAU7802_test\\src", "C:\\Users\\<USER>\\OneDrive\\Desktop\\10xconstruction\\embedded-test-framework\\firmware\\NAU7802_test\\.pio\\libdeps\\teensy41\\SparkFun Qwiic Scale NAU7802 Arduino Library\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Wire", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Wire\\utility", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\cores\\teensy4"], "compatlib": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\10xconstruction\\embedded-test-framework\\firmware\\NAU7802_test\\.pio\\libdeps\\teensy41\\SparkFun Qwiic Scale NAU7802 Arduino Library\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Wire", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Wire\\utility", "C:\\Users\\<USER>\\OneDrive\\Desktop\\10xconstruction\\embedded-test-framework\\firmware\\NAU7802_test\\.pio\\libdeps\\teensy41\\Unity\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\ADC", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\AccelStepper\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Adafruit_NeoPixel", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Adafruit_STMPE610", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Adafruit_VS1053", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Adafruit_nRF8001", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Adafruit_nRF8001\\utility", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\AltSoftSerial", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Artnet", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Audio", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Audio\\utility", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Bounce", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Bounce2\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\CapacitiveSensor", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\CryptoAccel\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\DS1307RTC", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\DmxSimple", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\DogLcd", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\EEPROM", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\EasyTransfer\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\EasyTransferI2C\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Encoder", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Encoder\\utility", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Entropy", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Ethernet\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\FNET\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\FastCRC", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\FastLED\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\FlexCAN", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\FlexCAN_T4", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\FlexIO_t4\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\FlexiTimer2", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\FreqCount", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\FreqMeasure", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\FreqMeasureMulti", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\FrequencyTimer2", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\ILI9341_t3", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\ILI9488_t3\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\IRremote\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Keypad\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\LedControl\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\LedDisplay", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\LiquidCrystal\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\LiquidCrystalFast", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\LittleFS\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\LowPower", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\MFRC522\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\MIDI\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Metro", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\MsTimer2", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\NXPMotionSense", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\NXPMotionSense\\utility", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\NativeEthernet\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\OSC", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\OctoWS2811", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\OneWire", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\PS2Keyboard", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\PS2Keyboard\\utility", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\PWMServo", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Ping", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\PulsePosition", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\QuadEncoder", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\RA8875\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\RadioHead", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\ResponsiveAnalogRead\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\SD\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\SPI", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\SPIFlash", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\ST7735_t3\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\SdFat\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\SerialFlash", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Servo", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\ShiftPWM", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Snooze\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\SoftPWM", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\SoftwareSerial", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\TFT_ILI9163C", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Talkie", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\TeensyThreads", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Time", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\TimeAlarms", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\TimerOne", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\TimerThree", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\TinyGPS", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\Tlc5940", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\TouchScreen", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\USBHost_t36", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\USBHost_t36\\utility", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\UTFT", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\VirtualWire", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\WS2812Serial", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\XBee", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\XPT2046_Touchscreen", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\i2c_t3", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\ks0108", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\ssd1351", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoteensy\\libraries\\x10"], "toolchain": ["C:\\Users\\<USER>\\.platformio\\packages\\toolchain-gccarmnoneeabi-teensy\\arm-none-eabi\\include\\c++\\11.3.1", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-gccarmnoneeabi-teensy\\arm-none-eabi\\include\\c++\\11.3.1\\arm-none-eabi", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-gccarmnoneeabi-teensy\\lib\\gcc\\arm-none-eabi\\11.3.1\\include", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-gccarmnoneeabi-teensy\\lib\\gcc\\arm-none-eabi\\11.3.1\\include-fixed", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-gccarmnoneeabi-teensy\\arm-none-eabi\\include"]}, "cc_flags": ["-Wall", "-ffunction-sections", "-fdata-sections", "-mthumb", "-mcpu=cortex-m7", "-nostdlib", "-mfloat-abi=hard", "-mfpu=fpv5-d16", "-O2"], "cxx_flags": ["-fno-exceptions", "-felide-constructors", "-fno-rtti", "-std=gnu++17", "-Wno-error=narrowing", "-fpermissive", "-fno-threadsafe-statics", "-Wall", "-ffunction-sections", "-fdata-sections", "-mthumb", "-mcpu=cortex-m7", "-nostdlib", "-mfloat-abi=hard", "-mfpu=fpv5-d16", "-O2"], "cc_path": "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-gccarmnoneeabi-teensy\\bin\\arm-none-eabi-gcc.exe", "cxx_path": "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-gccarmnoneeabi-teensy\\bin\\arm-none-eabi-g++.exe", "gdb_path": "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-gccarmnoneeabi-teensy\\bin\\arm-none-eabi-gdb.exe", "prog_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\10xconstruction\\embedded-test-framework\\firmware\\NAU7802_test\\.pio\\build\\teensy41\\firmware.elf", "svd_path": null, "compiler_type": "gcc", "targets": [{"name": "upload", "group": "Platform", "title": "Upload"}], "extra": {"flash_images": []}}