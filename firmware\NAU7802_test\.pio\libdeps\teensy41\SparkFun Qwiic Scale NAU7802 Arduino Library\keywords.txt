#######################################
# Syntax Coloring Map
#######################################

#######################################
# Datatypes (KEYWORD1)
#######################################

NAU7802	KEYWORD1

#######################################
# Methods and Functions (KEYWORD2)
#######################################

begin	KEYWORD2
isConnected	KEYWORD2
available	KEYWORD2
getReading	KEYWORD2
getAverage	KEYWORD2

calculateZeroOffset	KEYWORD2
setZeroOffset	KEYWORD2
getZeroOffset	KEYWORD2

calculateCalibrationFactor	KEYWORD2
setCalibrationFactor	KEYWORD2
getCalibrationFactor	KEYWORD2

getWeight	KEYWORD2

setGain	KEYWORD2
setLDO	KEYWORD2
setSampleRate	KEYWORD2
setChannel	KEYWORD2
calibrateAFE	KEYWORD2
beginCalibrateAFE	KEYWORD2
calAFEStatus		KEYWORD2
waitForCalibrateAFE	KEYWORD2

reset	KEYWORD2
powerUp	KEYWORD2
powerDown	KEYWORD2
setIntPolarityHigh	KEYWORD2
setIntPolarityLow	KEYWORD2
getRevisionCode	KEYWORD2
setBit	KEYWORD2
clearBit	KEYWORD2
getBit	KEYWORD2
getRegister	KEYWORD2
setRegister	KEYWORD2

setLDORampDelay	KEYWORD2
getLDORampDelay	KEYWORD2

get24BitRegister	KEYWORD2
set24BitRegister	KEYWORD2
get32BitRegister	KEYWORD2
set32BitRegister	KEYWORD2

getChannel1Offset	KEYWORD2
setChannel1Offset	KEYWORD2
getChannel1Gain	KEYWORD2
setChannel1Gain	KEYWORD2


#######################################
# Constants (LITERAL1)
#######################################

NAU7802_PU_CTRL	LITERAL1
NAU7802_CTRL1	LITERAL1
NAU7802_CTRL2	LITERAL1
NAU7802_OCAL1_B2	LITERAL1
NAU7802_OCAL1_B1	LITERAL1
NAU7802_OCAL1_B0	LITERAL1
NAU7802_GCAL1_B3	LITERAL1
NAU7802_GCAL1_B2	LITERAL1
NAU7802_GCAL1_B1	LITERAL1
NAU7802_GCAL1_B0	LITERAL1
NAU7802_OCAL2_B2	LITERAL1
NAU7802_OCAL2_B1	LITERAL1
NAU7802_OCAL2_B0	LITERAL1
NAU7802_GCAL2_B3	LITERAL1
NAU7802_GCAL2_B2	LITERAL1
NAU7802_GCAL2_B1	LITERAL1
NAU7802_GCAL2_B0	LITERAL1
NAU7802_I2C_CONTROL	LITERAL1
NAU7802_ADCO_B2	LITERAL1
NAU7802_ADCO_B1	LITERAL1
NAU7802_ADCO_B0	LITERAL1
NAU7802_ADC	LITERAL1
NAU7802_OTP_B1	LITERAL1
NAU7802_OTP_B0	LITERAL1
NAU7802_DEVICE_REV	LITERAL1
NAU7802_PU_CTRL_RR	LITERAL1
NAU7802_PU_CTRL_PUD	LITERAL1
NAU7802_PU_CTRL_PUA	LITERAL1
NAU7802_PU_CTRL_PUR	LITERAL1
NAU7802_PU_CTRL_CS	LITERAL1
NAU7802_PU_CTRL_CR	LITERAL1
NAU7802_PU_CTRL_OSCS	LITERAL1
NAU7802_PU_CTRL_AVDDS	LITERAL1
NAU7802_CTRL1_GAIN	LITERAL1
NAU7802_CTRL1_VLDO	LITERAL1
NAU7802_CTRL1_DRDY_SEL	LITERAL1
NAU7802_CTRL1_CRP	LITERAL1
NAU7802_CTRL2_CALMOD	LITERAL1
NAU7802_CTRL2_CALS	LITERAL1
NAU7802_CTRL2_CAL_ERROR	LITERAL1
NAU7802_CTRL2_CRS	LITERAL1
NAU7802_CTRL2_CHS	LITERAL1
NAU7802_LDO_2V4	LITERAL1
NAU7802_LDO_2V7	LITERAL1
NAU7802_LDO_3V0	LITERAL1
NAU7802_LDO_3V3	LITERAL1
NAU7802_LDO_3V6	LITERAL1
NAU7802_LDO_3V9	LITERAL1
NAU7802_LDO_4V2	LITERAL1
NAU7802_LDO_4V5	LITERAL1
NAU7802_GAIN_128	LITERAL1
NAU7802_GAIN_64	LITERAL1
NAU7802_GAIN_32	LITERAL1
NAU7802_GAIN_16	LITERAL1
NAU7802_GAIN_8	LITERAL1
NAU7802_GAIN_4	LITERAL1
NAU7802_GAIN_2	LITERAL1
NAU7802_GAIN_1	LITERAL1
NAU7802_SPS_320	LITERAL1
NAU7802_SPS_80	LITERAL1
NAU7802_SPS_40	LITERAL1
NAU7802_SPS_20	LITERAL1
NAU7802_SPS_10	LITERAL1
NAU7802_CHANNEL_1	LITERAL1
NAU7802_CHANNEL_2	LITERAL1
NAU7802_PGA_CHP_DIS	LITERAL1
NAU7802_PGA_INV	LITERAL1
NAU7802_PGA_BYPASS_EN	LITERAL1
NAU7802_PGA_OUT_EN	LITERAL1
NAU7802_PGA_LDOMODE	LITERAL1
NAU7802_PGA_RD_OTP_SEL	LITERAL1
NAU7802_PGA_PWR_PGA_CURR	LITERAL1
NAU7802_PGA_PWR_ADC_CURR	LITERAL1
NAU7802_PGA_PWR_MSTR_BIAS_CURR	LITERAL1
NAU7802_PGA_PWR_PGA_CAP_EN	LITERAL1
NAU7802_CAL_SUCCESS		LITERAL1
NAU7802_CAL_IN_PROGRESS		LITERAL1
NAU7802_CAL_FAILURE		LITERAL1
NAU7802_CALMOD_INTERNAL		LITERAL1
NAU7802_CALMOD_OFFSET		LITERAL1
NAU7802_CALMOD_GAIN		LITERAL1
